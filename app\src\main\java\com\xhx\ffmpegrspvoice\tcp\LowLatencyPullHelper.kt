package com.xhx.ffmpegrspvoice.tcp

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.os.Build
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.blankj.utilcode.util.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.RandomAccessFile
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 低延迟拉流播放助手
 *
 * 优化策略：
 * 1. 最小化FFmpeg缓冲
 * 2. 减少AudioTrack缓冲区
 * 3. 更频繁的数据读取
 * 4. 优化文件IO
 */
object LowLatencyPullHelper {

    private const val TAG = "LowLatencyPullHelper"

    // 音频参数
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
    private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT

    // 低延迟参数
    private const val SMALL_BUFFER_SIZE = 1024  // 使用更小的缓冲区
    private const val READ_INTERVAL_MS = 20     // 更频繁的读取间隔

    // 拉流状态
    private val isPulling = AtomicBoolean(false)
    private var ffmpegExecutionId: Long = 0
    private var audioTrack: AudioTrack? = null
    private var playbackThread: Thread? = null

    // 文件读取位置
    private val fileReadPosition = AtomicLong(0)

    // 文件路径
    private val cacheDir = File(App.ctx.cacheDir, "low_latency_pull")
    private val outputPcmFile = File(cacheDir, "realtime_audio.pcm")

    /**
     * 开始低延迟拉流播放
     */
    fun startPulling(sdpFilePath: String): Boolean {
        if (isPulling.get()) {
            LogUtils.w(TAG, "拉流已在进行中")
            return false
        }

        val sdpFile = File(sdpFilePath)
        if (!sdpFile.exists()) {
            LogUtils.e(TAG, "SDP文件不存在: $sdpFilePath")
            return false
        }

        return try {
            initializeFiles()
            startLowLatencyDecoding(sdpFilePath)
            startLowLatencyPlayback()

            isPulling.set(true)
            fileReadPosition.set(0)
            LogUtils.i(TAG, "✅ 低延迟拉流播放启动成功")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ 低延迟拉流播放启动失败", e)
            stopPulling()
            false
        }
    }

    /**
     * 停止拉流
     */
    fun stopPulling() {
        if (!isPulling.get()) {
            return
        }

        isPulling.set(false)

        // 停止FFmpeg
        if (ffmpegExecutionId != 0L) {
            FFmpeg.cancel(ffmpegExecutionId)
            ffmpegExecutionId = 0
        }

        // 停止音频播放
        audioTrack?.stop()
        audioTrack?.release()
        audioTrack = null

        // 停止播放线程
        playbackThread?.interrupt()
        playbackThread = null

        LogUtils.i(TAG, "🛑 低延迟拉流已停止")
    }

    /**
     * 获取拉流状态
     */
    fun isPulling(): Boolean = isPulling.get()

    /**
     * 获取拉流信息
     */
    fun getPullInfo(): String {
        val fileSize = if (outputPcmFile.exists()) {
            "${outputPcmFile.length() / 1024}KB"
        } else {
            "0KB"
        }

        return "拉流状态: ${if (isPulling.get()) "进行中" else "已停止"}\n" +
                "解码文件: ${outputPcmFile.absolutePath}\n" +
                "文件大小: $fileSize\n" +
                "读取位置: ${fileReadPosition.get() / 1024}KB\n" +
                "优化模式: 低延迟"
    }

    /**
     * 初始化文件和目录
     */
    private fun initializeFiles() {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }

        // 清理旧文件
        if (outputPcmFile.exists()) {
            outputPcmFile.delete()
        }
    }

    /**
     * 启动低延迟FFmpeg解码
     */
    private fun startLowLatencyDecoding(sdpFilePath: String) {
        GlobalScope.launch(Dispatchers.IO) {
            // 减少等待时间
            Thread.sleep(500)

            // 构建超低延迟FFmpeg解码命令
            val command = "-protocol_whitelist file,udp,rtp " +
                    "-fflags +nobuffer+flush_packets " +      // 禁用缓冲并立即刷新
                    "-flags low_delay " +                     // 低延迟模式
                    "-probesize 32 " +                        // 最小探测大小
                    "-analyzeduration 0 " +                   // 不分析流
                    "-max_delay 0 " +                         // 最大延迟为0
                    "-reorder_queue_size 0 " +                // 禁用重排序队列
                    "-rw_timeout 3000000 " +                  // 3秒超时
                    "-i $sdpFilePath " +
                    "-acodec pcm_s16le " +
                    "-ar $SAMPLE_RATE " +
                    "-ac $CHANNELS " +
                    "-f s16le " +
                    "-flush_packets 1 " +                     // 立即刷新数据包
                    "${outputPcmFile.absolutePath}"

            LogUtils.i(TAG, "低延迟FFmpeg解码命令: $command")

            ffmpegExecutionId = FFmpeg.executeAsync(command) { executionId, returnCode ->
                LogUtils.i(TAG, "FFmpeg解码结束: executionId=$executionId, returnCode=$returnCode")

                when (returnCode) {
                    Config.RETURN_CODE_SUCCESS -> {
                        LogUtils.i(TAG, "✅ 解码正常结束")
                    }

                    Config.RETURN_CODE_CANCEL -> {
                        LogUtils.i(TAG, "🛑 解码被用户取消")
                    }

                    else -> {
                        LogUtils.w(TAG, "⚠️ 解码异常结束，返回码: $returnCode")
                    }
                }
            }
        }
    }

    /**
     * 启动低延迟音频播放
     */
    private fun startLowLatencyPlayback() {
        // 使用最小缓冲区
        val minBufferSize = AudioTrack.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_OUT_MONO,
            AUDIO_FORMAT
        )

        // 使用稍大于最小缓冲区的大小，但不要太大
        val bufferSize = minBufferSize * 2

        audioTrack = AudioTrack(
            AudioManager.STREAM_MUSIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_OUT_MONO,
            AUDIO_FORMAT,
            bufferSize,
            AudioTrack.MODE_STREAM
        )

        // 设置低延迟模式（API 26+）
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val audioAttributes = AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .setFlags(AudioAttributes.FLAG_LOW_LATENCY)
                    .build()

                audioTrack = AudioTrack.Builder()
                    .setAudioAttributes(audioAttributes)
                    .setAudioFormat(
                        AudioFormat.Builder()
                            .setEncoding(AUDIO_FORMAT)
                            .setSampleRate(SAMPLE_RATE)
                            .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                            .build()
                    )
                    .setBufferSizeInBytes(bufferSize)
                    .setTransferMode(AudioTrack.MODE_STREAM)
                    .build()
            }
        } catch (e: Exception) {
            LogUtils.w(TAG, "无法设置低延迟AudioTrack，使用默认配置", e)
        }

        audioTrack?.play()
        LogUtils.i(TAG, "AudioTrack初始化完成，缓冲区大小: $bufferSize")

        // 启动高频率播放线程
        playbackThread = Thread {
            val buffer = ByteArray(SMALL_BUFFER_SIZE)
            var totalPlayed = 0
            var lastLogTime = System.currentTimeMillis()

            LogUtils.i(TAG, "低延迟音频播放线程启动")

            while (isPulling.get() && !Thread.currentThread().isInterrupted) {
                try {
                    if (outputPcmFile.exists()) {
                        val currentFileSize = outputPcmFile.length()
                        val currentReadPos = fileReadPosition.get()

                        // 如果有新数据可读
                        if (currentFileSize > currentReadPos) {
                            RandomAccessFile(outputPcmFile, "r").use { randomAccessFile ->
                                randomAccessFile.seek(currentReadPos)

                                val bytesRead = randomAccessFile.read(buffer)
                                if (bytesRead > 0) {
                                    audioTrack?.write(buffer, 0, bytesRead)
                                    totalPlayed += bytesRead
                                    fileReadPosition.addAndGet(bytesRead.toLong())

                                    // 每2秒打印一次状态
                                    val currentTime = System.currentTimeMillis()
                                    if (currentTime - lastLogTime > 2000) {
                                        LogUtils.d(
                                            TAG,
                                            "🔊 低延迟播放: ${totalPlayed / 1024}KB, 延迟: ${(currentFileSize - currentReadPos) / 32}ms"
                                        )
                                        lastLogTime = currentTime
                                    }
                                }
                            }
                        }
                    }

                    // 高频率检查，减少延迟
                    Thread.sleep(READ_INTERVAL_MS.toLong())

                } catch (e: Exception) {
                    LogUtils.e(TAG, "低延迟播放异常", e)
                    Thread.sleep(100)
                }
            }

            LogUtils.i(TAG, "低延迟播放线程结束，总计播放: ${totalPlayed / 1024}KB")
        }

        playbackThread?.start()
    }
}
