package com.xhx.ffmpegrspvoice.tcp

import android.content.Context
import android.net.Uri
import android.util.Log
import org.videolan.libvlc.LibVLC
import org.videolan.libvlc.Media
import org.videolan.libvlc.MediaPlayer
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * VLC播放器帮助类
 * 用于播放本地SDP文件进行拉流
 */
object VLCPlayerHelper {
    
    private const val TAG = "VLCPlayerHelper"
    
    private var libVLC: LibVLC? = null
    private var mediaPlayer: MediaPlayer? = null
    private val isPlaying = AtomicBoolean(false)
    private val isInitialized = AtomicBoolean(false)
    
    /**
     * 初始化VLC
     */
    fun initialize(context: Context): Bo<PERSON>an {
        return try {
            if (isInitialized.get()) {
                Log.d(TAG, "VLC already initialized")
                return true
            }
            
            // VLC选项配置
            val options = arrayListOf<String>().apply {
                // 网络缓存设置（毫秒）- 增加缓存以处理网络抖动
                add("--network-caching=1000")
                add("--live-caching=1000")
                // 音频输出模块
                add("--aout=opensles")
                // 启用详细日志
                add("--verbose=2")
                // 组播相关设置
                add("--intf=dummy")
                add("--no-video")
                // RTP/UDP设置 - 移除TCP强制，允许UDP组播
                add("--rtp-client-port=15556")
                // 音频解码器
                add("--codec=any")
                // 实时流优化设置
                add("--clock-jitter=0")
                add("--clock-synchro=0")
                // 组播接收设置
                add("--miface-addr=0.0.0.0")  // 监听所有接口
                add("--ttl=1")  // 组播TTL
                // 缓冲区设置
                add("--file-caching=1000")
                add("--sout-mux-caching=1000")
            }
            
            libVLC = LibVLC(context, options)
            mediaPlayer = MediaPlayer(libVLC)
            
            // 设置事件监听器
            setupEventListeners()
            
            isInitialized.set(true)
            Log.i(TAG, "VLC initialized successfully with options: $options")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize VLC", e)
            false
        }
    }
    
    /**
     * 设置事件监听器
     */
    private fun setupEventListeners() {
        mediaPlayer?.setEventListener { event ->
            when (event.type) {
                MediaPlayer.Event.Playing -> {
                    Log.i(TAG, "Media started playing")
                    isPlaying.set(true)
                }
                MediaPlayer.Event.Paused -> {
                    Log.i(TAG, "Media paused")
                    isPlaying.set(false)
                }
                MediaPlayer.Event.Stopped -> {
                    Log.i(TAG, "Media stopped")
                    isPlaying.set(false)
                }
                MediaPlayer.Event.EndReached -> {
                    Log.i(TAG, "Media end reached")
                    isPlaying.set(false)
                }
                MediaPlayer.Event.EncounteredError -> {
                    Log.e(TAG, "Media player encountered error")
                    isPlaying.set(false)
                }
                MediaPlayer.Event.Buffering -> {
                    Log.d(TAG, "Buffering: ${event.buffering}%")
                }
                MediaPlayer.Event.Opening -> {
                    Log.i(TAG, "Media opening")
                }
                MediaPlayer.Event.MediaChanged -> {
                    Log.i(TAG, "Media changed")
                }
                else -> {
                    Log.d(TAG, "Media event: ${event.type}")
                }
            }
        }
    }
    
    /**
     * 播放SDP文件
     */
    fun playSdpFile(sdpFilePath: String): Boolean {
        return try {
            if (!isInitialized.get()) {
                Log.e(TAG, "VLC not initialized")
                return false
            }
            
            if (isPlaying.get()) {
                Log.w(TAG, "Already playing, stopping current playback")
                stop()
            }
            
            val sdpFile = File(sdpFilePath)
            if (!sdpFile.exists()) {
                Log.e(TAG, "SDP file does not exist: $sdpFilePath")
                return false
            }
            
            Log.i(TAG, "Playing SDP file: $sdpFilePath")
            Log.d(TAG, "SDP content:\n${sdpFile.readText()}")
            
            // 创建媒体对象
            val uri = Uri.fromFile(sdpFile)
            val media = Media(libVLC, uri)
            
            // 设置媒体选项 - 针对组播RTP流优化
            media.addOption(":network-caching=1000")  // 增加网络缓存
            media.addOption(":live-caching=1000")     // 增加实时缓存
            media.addOption(":file-caching=1000")     // 文件缓存
            media.addOption(":clock-jitter=0")        // 禁用时钟抖动补偿
            media.addOption(":clock-synchro=0")       // 禁用时钟同步
            media.addOption(":rtp-client-port=15556") // RTP客户端端口
            media.addOption(":miface-addr=0.0.0.0")   // 组播接口地址
            
            mediaPlayer?.media = media
            media.release()
            
            // 开始播放
            mediaPlayer?.play()
            
            Log.i(TAG, "Started playing SDP file")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to play SDP file: $sdpFilePath", e)
            false
        }
    }
    
    /**
     * 停止播放
     */
    fun stop() {
        try {
            mediaPlayer?.stop()
            isPlaying.set(false)
            Log.i(TAG, "Playback stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop playback", e)
        }
    }
    
    /**
     * 暂停播放
     */
    fun pause() {
        try {
            if (isPlaying.get()) {
                mediaPlayer?.pause()
                Log.i(TAG, "Playback paused")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to pause playback", e)
        }
    }
    
    /**
     * 恢复播放
     */
    fun resume() {
        try {
            if (!isPlaying.get()) {
                mediaPlayer?.play()
                Log.i(TAG, "Playback resumed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to resume playback", e)
        }
    }
    
    /**
     * 检查是否正在播放
     */
    fun isPlaying(): Boolean = isPlaying.get()
    
    /**
     * 获取播放状态信息
     */
    fun getPlaybackInfo(): String {
        return try {
            val player = mediaPlayer ?: return "MediaPlayer not initialized"
            val media = player.media ?: return "No media loaded"
            
            buildString {
                append("播放状态: ${if (isPlaying.get()) "播放中" else "已停止"}\n")
                append("媒体URI: ${media.uri}\n")
                append("时长: ${player.length}ms\n")
                append("当前位置: ${player.time}ms\n")
                append("音量: ${player.volume}\n")
                append("播放速率: ${player.rate}\n")
            }
        } catch (e: Exception) {
            "获取播放信息失败: ${e.message}"
        }
    }
    
    /**
     * 设置音量 (0-100)
     */
    fun setVolume(volume: Int) {
        try {
            val clampedVolume = volume.coerceIn(0, 100)
            mediaPlayer?.volume = clampedVolume
            Log.d(TAG, "Volume set to: $clampedVolume")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set volume", e)
        }
    }
    
    /**
     * 获取当前音量
     */
    fun getVolume(): Int {
        return try {
            mediaPlayer?.volume ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get volume", e)
            0
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            stop()
            mediaPlayer?.release()
            libVLC?.release()
            mediaPlayer = null
            libVLC = null
            isInitialized.set(false)
            isPlaying.set(false)
            Log.i(TAG, "VLC resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release VLC resources", e)
        }
    }
}
