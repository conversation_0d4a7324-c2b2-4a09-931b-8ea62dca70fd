package com.xhx.megaphone.tcp

import com.blankj.utilcode.util.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.net.Inet4Address
import java.net.InetAddress
import java.net.MulticastSocket
import java.net.NetworkInterface
import java.net.SocketTimeoutException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 网络诊断工具
 *
 * 功能：
 * 1. 检测组播网络连通性
 * 2. 监听组播数据包
 * 3. 网络延迟测试
 */
object NetworkDiagnostics {

    private const val TAG = "NetworkDiagnostics"
    private const val MULTICAST_ADDRESS = "***********"
    private const val MULTICAST_PORT = 15556

    private val isMonitoring = AtomicBoolean(false)
    private var monitoringThread: Thread? = null

    /**
     * 检测组播网络环境
     */
    fun checkMulticastEnvironment(): String {
        val result = StringBuilder()

        try {
            // 1. 检查网络接口
            result.append("=== 网络接口检查 ===\n")
            val interfaces = NetworkInterface.getNetworkInterfaces()
            var hasMulticastInterface = false

            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                if (networkInterface.isUp && !networkInterface.isLoopback) {
                    result.append("接口: ${networkInterface.displayName}\n")
                    result.append("  - 支持组播: ${networkInterface.supportsMulticast()}\n")
                    result.append("  - MTU: ${networkInterface.mtu}\n")

                    if (networkInterface.supportsMulticast()) {
                        hasMulticastInterface = true
                    }

                    // 显示IP地址
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (address is Inet4Address && !address.isLoopbackAddress) {
                            result.append("  - IPv4: ${address.hostAddress}\n")
                        }
                    }
                    result.append("\n")
                }
            }

            result.append("组播接口可用: ${if (hasMulticastInterface) "✅" else "❌"}\n\n")

            // 2. 测试组播地址
            result.append("=== 组播地址测试 ===\n")
            try {
                val multicastAddress = InetAddress.getByName(MULTICAST_ADDRESS)
                result.append("组播地址: $MULTICAST_ADDRESS\n")
                result.append("地址类型: ${if (multicastAddress.isMulticastAddress) "组播地址 ✅" else "非组播地址 ❌"}\n")
                result.append("可达性: ${if (multicastAddress.isReachable(5000)) "可达 ✅" else "不可达 ⚠️"}\n\n")
            } catch (e: Exception) {
                result.append("组播地址解析失败: ${e.message}\n\n")
            }

            // 3. 测试UDP端口
            result.append("=== UDP端口测试 ===\n")
            try {
                val socket = DatagramSocket()
                socket.soTimeout = 3000

                val testData = "PING".toByteArray()
                val packet = DatagramPacket(
                    testData,
                    testData.size,
                    InetAddress.getByName(MULTICAST_ADDRESS),
                    MULTICAST_PORT
                )

                socket.send(packet)
                result.append("UDP发送测试: 成功 ✅\n")
                socket.close()
            } catch (e: Exception) {
                result.append("UDP发送测试: 失败 ❌ (${e.message})\n")
            }

            result.append("\n")

        } catch (e: Exception) {
            result.append("网络检查异常: ${e.message}\n")
        }

        return result.toString()
    }

    /**
     * 开始监听组播数据包
     */
    fun startMulticastMonitoring(): Boolean {
        if (isMonitoring.get()) {
            LogUtils.w(TAG, "组播监听已在进行中")
            return false
        }

        return try {
            isMonitoring.set(true)

            monitoringThread = Thread {
                var multicastSocket: MulticastSocket? = null

                try {
                    LogUtils.i(TAG, "开始监听组播数据包: $MULTICAST_ADDRESS:$MULTICAST_PORT")

                    multicastSocket = MulticastSocket(MULTICAST_PORT)
                    val group = InetAddress.getByName(MULTICAST_ADDRESS)
                    multicastSocket.joinGroup(group)
                    multicastSocket.soTimeout = 5000 // 5秒超时

                    val buffer = ByteArray(1024)
                    var packetCount = 0
                    var totalBytes = 0
                    var lastLogTime = System.currentTimeMillis()

                    while (isMonitoring.get() && !Thread.currentThread().isInterrupted) {
                        try {
                            val packet = DatagramPacket(buffer, buffer.size)
                            multicastSocket.receive(packet)

                            packetCount++
                            totalBytes += packet.length

                            // 每5秒打印一次统计
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastLogTime > 5000) {
                                LogUtils.i(
                                    TAG,
                                    "📦 接收组播数据: ${packetCount}包, ${totalBytes / 1024}KB"
                                )
                                lastLogTime = currentTime
                            }

                        } catch (e: SocketTimeoutException) {
                            // 超时是正常的，继续监听
                            LogUtils.d(TAG, "监听超时，继续等待数据包...")
                        }
                    }

                    LogUtils.i(TAG, "组播监听结束，总计: ${packetCount}包, ${totalBytes / 1024}KB")

                } catch (e: Exception) {
                    LogUtils.e(TAG, "组播监听异常", e)
                } finally {
                    try {
                        multicastSocket?.leaveGroup(InetAddress.getByName(MULTICAST_ADDRESS))
                        multicastSocket?.close()
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "关闭组播socket异常", e)
                    }
                }
            }

            monitoringThread?.start()
            LogUtils.i(TAG, "✅ 组播监听启动成功")
            true

        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ 组播监听启动失败", e)
            isMonitoring.set(false)
            false
        }
    }

    /**
     * 停止监听组播数据包
     */
    fun stopMulticastMonitoring() {
        if (!isMonitoring.get()) {
            return
        }

        isMonitoring.set(false)
        monitoringThread?.interrupt()
        monitoringThread = null

        LogUtils.i(TAG, "🛑 组播监听已停止")
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring.get()

    /**
     * 测试网络延迟
     */
    fun testNetworkLatency(callback: (String) -> Unit) {
        GlobalScope.launch(Dispatchers.IO) {
            val result = StringBuilder()
            result.append("=== 网络延迟测试 ===\n")

            val testAddresses = listOf(
                "*******" to "Google DNS",
                "***************" to "114 DNS",
                "***********" to "本地网关"
            )

            for ((address, name) in testAddresses) {
                try {
                    val startTime = System.currentTimeMillis()
                    val inetAddress = InetAddress.getByName(address)
                    val reachable = inetAddress.isReachable(3000)
                    val endTime = System.currentTimeMillis()
                    val latency = endTime - startTime

                    result.append("$name ($address): ")
                    if (reachable) {
                        result.append("${latency}ms ✅\n")
                    } else {
                        result.append("不可达 ❌\n")
                    }
                } catch (e: Exception) {
                    result.append("$name ($address): 异常 ❌ (${e.message})\n")
                }
            }

            GlobalScope.launch(Dispatchers.Main) {
                callback(result.toString())
            }
        }
    }
}
