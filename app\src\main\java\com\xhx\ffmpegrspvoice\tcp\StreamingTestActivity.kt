package com.xhx.ffmpegrspvoice.tcp

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ToastUtils
import com.xhx.ffmpegrspvoice.R
import com.xhx.megaphone.tcp.NetworkDiagnostics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 组播音频推拉流测试Activity
 *
 * 功能：
 * 1. 本地音频推流到组播地址 225.0.0.251:15556
 * 2. 从组播地址拉流并播放音频
 * 3. 使用FFmpeg进行音频编码和解码
 * 4. 支持实时录音推流
 */
class StreamingTestActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "StreamingTestActivity"
        private const val REQUEST_PERMISSIONS = 1001
    }

    // UI组件
    private lateinit var btnStartPush: Button
    private lateinit var btnStopPush: Button
    private lateinit var btnStartPull: Button
    private lateinit var btnLowLatencyPull: Button
    private lateinit var btnStopPull: Button
    private lateinit var btnVlcPlay: Button
    private lateinit var btnVlcPause: Button
    private lateinit var btnVlcStop: Button
    private lateinit var btnNetworkCheck: Button
    private lateinit var btnMonitorMulticast: Button
    private lateinit var tvStatus: TextView

    // 拉流相关
    private val isPulling = AtomicBoolean(false)
    private var ffmpegPullExecutionId: Long = 0

    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_streaming_test)

        initViews()
        initVLC()
        checkPermissions()
        updateStatus("等待操作...")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String?>,
        grantResults: IntArray,
        deviceId: Int
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults, deviceId)
    }

    private fun initViews() {
        btnStartPush = findViewById(R.id.btn_start_push)
        btnStopPush = findViewById(R.id.btn_stop_push)
        btnStartPull = findViewById(R.id.btn_start_pull)
        btnLowLatencyPull = findViewById(R.id.btn_low_latency_pull)
        btnStopPull = findViewById(R.id.btn_stop_pull)
        btnVlcPlay = findViewById(R.id.btn_vlc_play)
        btnVlcPause = findViewById(R.id.btn_vlc_pause)
        btnVlcStop = findViewById(R.id.btn_vlc_stop)
        btnNetworkCheck = findViewById(R.id.btn_network_check)
        btnMonitorMulticast = findViewById(R.id.btn_monitor_multicast)
        tvStatus = findViewById(R.id.tv_status)

        btnStartPush.setOnClickListener { startPushStream() }
        btnStopPush.setOnClickListener { stopPushStream() }
        btnStartPull.setOnClickListener { startPullStream() }
        btnLowLatencyPull.setOnClickListener { startLowLatencyPullStream() }
        btnStopPull.setOnClickListener { stopPullStream() }
        btnVlcPlay.setOnClickListener { startVlcPlayback() }
        btnVlcPause.setOnClickListener { pauseVlcPlayback() }
        btnVlcStop.setOnClickListener { stopVlcPlayback() }
        btnNetworkCheck.setOnClickListener { performNetworkCheck() }
        btnMonitorMulticast.setOnClickListener { toggleMulticastMonitoring() }

        updateUI()
    }

    /**
     * 初始化VLC播放器
     */
    private fun initVLC() {
        GlobalScope.launch(Dispatchers.IO) {
            val success = VLCPlayerHelper.initialize(this@StreamingTestActivity)

            handler.post {
                if (success) {
                    updateStatus("✅ VLC播放器初始化成功")
                } else {
                    updateStatus("❌ VLC播放器初始化失败")
                }
            }
        }
    }

    private fun checkPermissions() {
        val permissions = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )

        val missingPermissions = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                this,
                missingPermissions.toTypedArray(),
                REQUEST_PERMISSIONS
            )
        }
    }

    /**
     * 开始推流
     */
    private fun startPushStream() {
        if (LiveStreamingHelper.isStreaming()) {
            ToastUtils.showShort("正在推流中...")
            return
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED
        ) {
            ToastUtils.showShort("需要录音权限")
            return
        }

        GlobalScope.launch(Dispatchers.IO) {
            val success = LiveStreamingHelper.startStreaming()

            handler.post {
                if (success) {
                    updateUI()
                    updateStatus("✅ 开始实时录音推流（低延迟）\n${LiveStreamingHelper.getMulticastInfo()}")
                    ToastUtils.showShort("实时推流启动成功")
                } else {
                    ToastUtils.showShort("推流启动失败")
                    updateStatus("❌ 推流启动失败")
                }
            }
        }
    }

    /**
     * 停止推流
     */
    private fun stopPushStream() {
        if (!LiveStreamingHelper.isStreaming()) {
            return
        }

        LiveStreamingHelper.stopStreaming()
        updateUI()
        updateStatus("🛑 实时推流已停止")
        ToastUtils.showShort("推流已停止")
    }

    /**
     * 开始拉流
     */
    private fun startPullStream() {
        if (PullStreamHelper.isPulling()) {
            ToastUtils.showShort("正在拉流中...")
            return
        }

        val sdpFilePath = LiveStreamingHelper.getSdpFilePath()

        // 显示SDP文件内容
        val sdpContent = showSdpContent(sdpFilePath)
        updateStatus("📡 准备开始拉流播放\n\n$sdpContent")

        // 启动拉流播放
        val success = PullStreamHelper.startPulling(sdpFilePath)

        if (success) {
            isPulling.set(true)
            updateUI()
            updateStatus("📡 开始FFmpeg拉流解码播放\n\n$sdpContent\n\n${PullStreamHelper.getPullInfo()}")
            ToastUtils.showShort("拉流播放启动成功")
        } else {
            ToastUtils.showShort("拉流启动失败")
            updateStatus("❌ 拉流启动失败\n\n$sdpContent")
        }
    }

    /**
     * 停止拉流
     */
    /**
     * 开始低延迟拉流
     */
    private fun startLowLatencyPullStream() {
        if (LowLatencyPullHelper.isPulling() || PullStreamHelper.isPulling()) {
            ToastUtils.showShort("正在拉流中...")
            return
        }

        val sdpFilePath = LiveStreamingHelper.getSdpFilePath()

        // 显示SDP文件内容
        val sdpContent = showSdpContent(sdpFilePath)
        updateStatus("🚀 准备开始低延迟拉流播放\n\n$sdpContent")

        // 启动低延迟拉流播放
        val success = LowLatencyPullHelper.startPulling(sdpFilePath)

        if (success) {
            isPulling.set(true)
            updateUI()
            updateStatus("🚀 开始低延迟FFmpeg拉流解码播放\n\n$sdpContent\n\n${LowLatencyPullHelper.getPullInfo()}")
            ToastUtils.showShort("低延迟拉流播放启动成功")
        } else {
            ToastUtils.showShort("低延迟拉流启动失败")
            updateStatus("❌ 低延迟拉流启动失败\n\n$sdpContent")
        }
    }

    private fun stopPullStream() {
        if (!PullStreamHelper.isPulling() && !LowLatencyPullHelper.isPulling()) {
            return
        }

        PullStreamHelper.stopPulling()
        LowLatencyPullHelper.stopPulling()
        isPulling.set(false)
        updateUI()
        updateStatus("🛑 拉流已停止")
        ToastUtils.showShort("拉流已停止")
    }

    /**
     * 显示SDP文件内容
     */
    private fun showSdpContent(sdpFilePath: String): String {
        return try {
            val sdpFile = File(sdpFilePath)
            if (sdpFile.exists()) {
                val content = sdpFile.readText()
                "SDP文件内容:\n$content"
            } else {
                "SDP文件不存在: $sdpFilePath"
            }
        } catch (e: Exception) {
            "读取SDP文件失败: ${e.message}"
        }
    }


    /**
     * 更新UI状态
     */
    private fun updateUI() {
        val isStreamingActive = LiveStreamingHelper.isStreaming()
        val isPullingActive = PullStreamHelper.isPulling() || LowLatencyPullHelper.isPulling()
        val isVlcPlaying = VLCPlayerHelper.isPlaying()

        btnStartPush.isEnabled = !isStreamingActive
        btnStopPush.isEnabled = isStreamingActive
        btnStartPull.isEnabled = !isPullingActive && !isVlcPlaying
        btnLowLatencyPull.isEnabled = !isPullingActive && !isVlcPlaying
        btnStopPull.isEnabled = isPullingActive
        btnVlcPlay.isEnabled = !isVlcPlaying && !isPullingActive
        btnVlcPause.isEnabled = isVlcPlaying
        btnVlcStop.isEnabled = isVlcPlaying
    }

    /**
     * 更新状态显示
     */
    private fun updateStatus(status: String) {
        handler.post {
            tvStatus.text = status
            LogUtils.i(TAG, status)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == REQUEST_PERMISSIONS) {
            val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
            if (allGranted) {
                ToastUtils.showShort("权限已授予")
            } else {
                ToastUtils.showShort("需要相关权限才能正常使用")
            }
        }
    }

    /**
     * 执行网络检查
     */
    private fun performNetworkCheck() {
        updateStatus("🔍 正在进行网络环境检查...")

        GlobalScope.launch(Dispatchers.IO) {
            val result = NetworkDiagnostics.checkMulticastEnvironment()

            handler.post {
                updateStatus("📋 网络环境检查结果：\n$result")
            }

            // 同时进行延迟测试
            NetworkDiagnostics.testNetworkLatency { latencyResult ->
                handler.post {
                    val currentStatus = tvStatus.text.toString()
                    updateStatus("$currentStatus\n$latencyResult")
                }
            }
        }
    }

    /**
     * 切换组播监听
     */
    private fun toggleMulticastMonitoring() {
        if (NetworkDiagnostics.isMonitoring()) {
            NetworkDiagnostics.stopMulticastMonitoring()
            btnMonitorMulticast.text = "监听组播"
            btnMonitorMulticast.setBackgroundColor(getColor(R.color.holo_blue_dark))
            updateStatus("🛑 组播监听已停止")
        } else {
            val success = NetworkDiagnostics.startMulticastMonitoring()
            if (success) {
                btnMonitorMulticast.text = "停止监听"
                btnMonitorMulticast.setBackgroundColor(getColor(R.color.holo_red_dark))
                updateStatus("👂 开始监听组播数据包 225.0.0.251:15556\n请在另一设备上启动推流进行测试")
            } else {
                updateStatus("❌ 组播监听启动失败")
            }
        }
    }

    /**
     * 开始VLC播放
     */
    private fun startVlcPlayback() {
        if (VLCPlayerHelper.isPlaying()) {
            ToastUtils.showShort("VLC正在播放中...")
            return
        }

        GlobalScope.launch(Dispatchers.IO) {
            // 首先尝试使用现有的SDP文件
            var sdpFilePath: String? = LiveStreamingHelper.getSdpFilePath()

            // 如果现有SDP文件不存在或无效，创建一个新的
            if (sdpFilePath == null || !SdpFileManager.validateSdpFile(sdpFilePath)) {
                LogUtils.w(TAG, "现有SDP文件无效，创建新的SDP文件")
                sdpFilePath = SdpFileManager.createAudioMulticastSdp(
                    this@StreamingTestActivity,
                    "225.0.0.251",
                    15556,
                    "opus",
                    48000,
                    2
                ) ?: SdpFileManager.copySampleSdpFromAssets(this@StreamingTestActivity)

                if (sdpFilePath == null) {
                    handler.post {
                        ToastUtils.showShort("无法创建或获取SDP文件")
                        updateStatus("❌ 无法创建或获取SDP文件")
                    }
                    return@launch
                }
            }

            // 显示SDP文件内容
            val sdpContent = showSdpContent(sdpFilePath)

            handler.post {
                updateStatus("🎵 准备使用VLC播放SDP文件\n\n$sdpContent")
            }

            val success = VLCPlayerHelper.playSdpFile(sdpFilePath)

            handler.post {
                if (success) {
                    updateUI()
                    updateStatus("🎵 VLC开始播放SDP文件\n\n$sdpContent\n\n${VLCPlayerHelper.getPlaybackInfo()}")
                    ToastUtils.showShort("VLC播放启动成功")
                } else {
                    ToastUtils.showShort("VLC播放启动失败")
                    updateStatus("❌ VLC播放启动失败\n\n$sdpContent")
                }
            }
        }
    }

    /**
     * 暂停VLC播放
     */
    private fun pauseVlcPlayback() {
        VLCPlayerHelper.pause()
        updateUI()
        updateStatus("⏸️ VLC播放已暂停\n\n${VLCPlayerHelper.getPlaybackInfo()}")
        ToastUtils.showShort("VLC播放已暂停")
    }

    /**
     * 停止VLC播放
     */
    private fun stopVlcPlayback() {
        VLCPlayerHelper.stop()
        updateUI()
        updateStatus("🛑 VLC播放已停止")
        ToastUtils.showShort("VLC播放已停止")
    }

    override fun onDestroy() {
        super.onDestroy()
        stopPushStream()
        stopPullStream()
        stopVlcPlayback()
        VLCPlayerHelper.release()
        NetworkDiagnostics.stopMulticastMonitoring()
    }
}
