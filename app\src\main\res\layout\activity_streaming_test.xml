<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="组播音频推拉流测试"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="16dp"
        android:background="#2196F3"
        android:textColor="#ffffff"
        android:layout_marginBottom="24dp" />

    <!-- 推流控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="16dp"
        android:layout_marginBottom="16dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="音频推流控制"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="推流地址: rtp://225.0.0.251:15556"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_start_push"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="开始推流"
                android:textColor="#ffffff"
                android:background="#4CAF50"
                android:layout_marginEnd="8dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_stop_push"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="停止推流"
                android:textColor="#ffffff"
                android:background="#F44336"
                android:layout_marginStart="8dp"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 拉流控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="16dp"
        android:layout_marginBottom="16dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="音频拉流控制"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="拉流方式: 通过SDP文件从组播地址接收"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_start_pull"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="普通拉流"
                android:textColor="#ffffff"
                android:background="#2196F3"
                android:layout_marginEnd="4dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_low_latency_pull"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="低延迟拉流"
                android:textColor="#ffffff"
                android:background="#4CAF50"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_stop_pull"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="停止拉流"
                android:textColor="#ffffff"
                android:background="#FF9800"
                android:layout_marginStart="4dp"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- VLC播放器控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="16dp"
        android:layout_marginBottom="16dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="VLC播放器控制"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用VLC播放器播放SDP文件进行拉流"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_vlc_play"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="VLC播放"
                android:textColor="#ffffff"
                android:background="#FF5722"
                android:layout_marginEnd="4dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_vlc_pause"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="暂停"
                android:textColor="#ffffff"
                android:background="#FFC107"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_vlc_stop"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="停止"
                android:textColor="#ffffff"
                android:background="#9E9E9E"
                android:layout_marginStart="4dp"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 状态显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="16dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="运行状态"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="#f9f9f9"
            android:padding="8dp">

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="等待操作..."
                android:textSize="14sp"
                android:textColor="#333333"
                android:lineSpacingExtra="4dp" />

        </ScrollView>

    </LinearLayout>

    <!-- 网络诊断区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_network_check"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="网络检查"
            android:textColor="#ffffff"
            android:background="#9C27B0"
            android:layout_marginEnd="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/btn_monitor_multicast"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="监听组播"
            android:textColor="#ffffff"
            android:background="#607D8B"
            android:layout_marginStart="8dp"
            android:padding="12dp" />

    </LinearLayout>

    <!-- 说明信息 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="说明：\n1. 推流：录制本地麦克风音频并推送到组播地址\n2. 拉流：从组播地址接收音频流并播放\n3. 支持局域网内多设备同时接收\n4. 如果拉流有问题，请先进行网络检查"
        android:textSize="12sp"
        android:textColor="#666666"
        android:background="#e8f5e8"
        android:padding="12dp"
        android:layout_marginTop="16dp" />

</LinearLayout>
