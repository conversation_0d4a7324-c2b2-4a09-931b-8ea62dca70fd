package com.xhx.ffmpegrspvoice.tcp

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * SDP文件管理器
 * 用于创建和管理SDP文件
 */
object SdpFileManager {
    
    private const val TAG = "SdpFileManager"
    
    /**
     * 创建标准的音频组播SDP文件
     */
    fun createAudioMulticastSdp(
        context: Context,
        multicastAddress: String = "***********",
        port: Int = 15556,
        codec: String = "opus",
        sampleRate: Int = 48000,
        channels: Int = 2
    ): String? {
        return try {
            val sdpContent = buildString {
                append("v=0\n")
                append("o=- 0 0 IN IP4 $multicastAddress\n")
                append("s=Audio Stream\n")
                append("c=IN IP4 $multicastAddress/255\n")
                append("t=0 0\n")
                append("m=audio $port RTP/AVP 96\n")
                
                when (codec.lowercase()) {
                    "opus" -> {
                        append("a=rtpmap:96 opus/$sampleRate/$channels\n")
                        append("a=fmtp:96 sprop-stereo=1\n")
                    }
                    "pcm" -> {
                        append("a=rtpmap:96 L16/$sampleRate/$channels\n")
                    }
                    "aac" -> {
                        append("a=rtpmap:96 mpeg4-generic/$sampleRate/$channels\n")
                        append("a=fmtp:96 streamtype=5;profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3;config=1190\n")
                    }
                    else -> {
                        append("a=rtpmap:96 opus/$sampleRate/$channels\n")
                        append("a=fmtp:96 sprop-stereo=1\n")
                    }
                }
            }
            
            val fileName = "audio_multicast_${System.currentTimeMillis()}.sdp"
            val file = File(context.filesDir, fileName)
            
            FileOutputStream(file).use { fos ->
                fos.write(sdpContent.toByteArray())
            }
            
            Log.i(TAG, "Created SDP file: ${file.absolutePath}")
            Log.d(TAG, "SDP content:\n$sdpContent")
            
            file.absolutePath
        } catch (e: IOException) {
            Log.e(TAG, "Failed to create SDP file", e)
            null
        }
    }
    
    /**
     * 创建RTP流SDP文件
     */
    fun createRtpStreamSdp(
        context: Context,
        rtpUrl: String,
        codec: String = "opus",
        sampleRate: Int = 48000,
        channels: Int = 2
    ): String? {
        return try {
            // 解析RTP URL
            val uri = android.net.Uri.parse(rtpUrl)
            val host = uri.host ?: "127.0.0.1"
            val port = if (uri.port != -1) uri.port else 15556
            
            val sdpContent = buildString {
                append("v=0\n")
                append("o=- 0 0 IN IP4 $host\n")
                append("s=RTP Audio Stream\n")
                append("c=IN IP4 $host\n")
                append("t=0 0\n")
                append("m=audio $port RTP/AVP 96\n")
                
                when (codec.lowercase()) {
                    "opus" -> {
                        append("a=rtpmap:96 opus/$sampleRate/$channels\n")
                        append("a=fmtp:96 sprop-stereo=1\n")
                    }
                    "pcm" -> {
                        append("a=rtpmap:96 L16/$sampleRate/$channels\n")
                    }
                    "aac" -> {
                        append("a=rtpmap:96 mpeg4-generic/$sampleRate/$channels\n")
                        append("a=fmtp:96 streamtype=5;profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3;config=1190\n")
                    }
                    else -> {
                        append("a=rtpmap:96 opus/$sampleRate/$channels\n")
                        append("a=fmtp:96 sprop-stereo=1\n")
                    }
                }
            }
            
            val fileName = "rtp_stream_${System.currentTimeMillis()}.sdp"
            val file = File(context.filesDir, fileName)
            
            FileOutputStream(file).use { fos ->
                fos.write(sdpContent.toByteArray())
            }
            
            Log.i(TAG, "Created RTP SDP file: ${file.absolutePath}")
            Log.d(TAG, "SDP content:\n$sdpContent")
            
            file.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create RTP SDP file", e)
            null
        }
    }
    
    /**
     * 从assets复制示例SDP文件
     */
    fun copySampleSdpFromAssets(context: Context): String? {
        return try {
            val assetManager = context.assets
            val inputStream = assetManager.open("sample.sdp")
            
            val fileName = "sample_${System.currentTimeMillis()}.sdp"
            val file = File(context.filesDir, fileName)
            
            FileOutputStream(file).use { fos ->
                inputStream.copyTo(fos)
            }
            
            inputStream.close()
            
            Log.i(TAG, "Copied sample SDP file: ${file.absolutePath}")
            file.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy sample SDP file from assets", e)
            null
        }
    }
    
    /**
     * 验证SDP文件格式
     */
    fun validateSdpFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            if (!file.exists()) {
                Log.w(TAG, "SDP file does not exist: $filePath")
                return false
            }
            
            val content = file.readText()
            val lines = content.split("\n")
            
            // 基本SDP格式验证
            var hasVersion = false
            var hasOrigin = false
            var hasSession = false
            var hasMedia = false
            
            for (line in lines) {
                when {
                    line.startsWith("v=") -> hasVersion = true
                    line.startsWith("o=") -> hasOrigin = true
                    line.startsWith("s=") -> hasSession = true
                    line.startsWith("m=") -> hasMedia = true
                }
            }
            
            val isValid = hasVersion && hasOrigin && hasSession && hasMedia
            
            if (isValid) {
                Log.i(TAG, "SDP file validation passed: $filePath")
            } else {
                Log.w(TAG, "SDP file validation failed: $filePath")
                Log.d(TAG, "Missing elements - Version: $hasVersion, Origin: $hasOrigin, Session: $hasSession, Media: $hasMedia")
            }
            
            isValid
        } catch (e: Exception) {
            Log.e(TAG, "Failed to validate SDP file: $filePath", e)
            false
        }
    }
    
    /**
     * 读取SDP文件内容
     */
    fun readSdpContent(filePath: String): String? {
        return try {
            val file = File(filePath)
            if (file.exists()) {
                file.readText()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read SDP file: $filePath", e)
            null
        }
    }
    
    /**
     * 删除SDP文件
     */
    fun deleteSdpFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            val deleted = file.delete()
            if (deleted) {
                Log.i(TAG, "Deleted SDP file: $filePath")
            } else {
                Log.w(TAG, "Failed to delete SDP file: $filePath")
            }
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting SDP file: $filePath", e)
            false
        }
    }
}
