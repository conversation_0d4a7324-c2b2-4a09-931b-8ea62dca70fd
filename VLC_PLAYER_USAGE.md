# VLC Android 播放器使用说明

## 概述

本项目已集成 VLC Android 库 (`org.videolan.android:libvlc-all:3.4.4`) 来播放本地 SDP 文件进行拉流。VLC 播放器提供了强大的媒体播放能力，支持多种音频格式和网络协议。

## 功能特性

### 1. VLC 播放器集成
- **VLCPlayerHelper**: 封装了 VLC 播放器的核心功能
- **自动初始化**: 应用启动时自动初始化 VLC 库
- **事件监听**: 监听播放状态变化
- **资源管理**: 自动管理 VLC 资源的创建和释放

### 2. SDP 文件管理
- **SdpFileManager**: 提供 SDP 文件的创建、验证和管理功能
- **多种格式支持**: 支持 Opus、PCM、AAC 等音频编码格式
- **自动创建**: 如果 SDP 文件不存在，自动创建标准格式的 SDP 文件
- **格式验证**: 验证 SDP 文件格式的正确性

### 3. 播放控制
- **播放**: 开始播放 SDP 文件
- **暂停**: 暂停当前播放
- **停止**: 停止播放并释放资源
- **音量控制**: 支持音量调节 (0-100)

## 使用方法

### 1. 基本播放流程

1. **启动推流**: 首先点击"开始推流"按钮开始音频推流
2. **VLC播放**: 点击"VLC播放"按钮使用 VLC 播放器播放 SDP 文件
3. **控制播放**: 使用"暂停"和"停止"按钮控制播放状态

### 2. SDP 文件处理

应用会按以下优先级处理 SDP 文件：

1. **使用现有 SDP 文件**: 如果推流已生成 SDP 文件，直接使用
2. **创建新 SDP 文件**: 如果现有文件无效，自动创建标准格式的 SDP 文件
3. **使用示例文件**: 如果创建失败，从 assets 复制示例 SDP 文件

### 3. 支持的音频格式

#### Opus (推荐)
```sdp
m=audio 15556 RTP/AVP 96
a=rtpmap:96 opus/48000/2
a=fmtp:96 sprop-stereo=1
```

#### PCM
```sdp
m=audio 15556 RTP/AVP 96
a=rtpmap:96 L16/48000/2
```

#### AAC
```sdp
m=audio 15556 RTP/AVP 96
a=rtpmap:96 mpeg4-generic/48000/2
a=fmtp:96 streamtype=5;profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3;config=1190
```

## VLC 配置选项

VLC 播放器使用以下优化配置：

```kotlin
val options = arrayListOf<String>().apply {
    add("--network-caching=300")     // 网络缓存300ms
    add("--aout=opensles")           // 使用OpenSL ES音频输出
    add("--verbose=2")               // 详细日志
    add("--rtsp-tcp")                // RTSP使用TCP
    add("--rtp-client-port=15556")   // RTP客户端端口
    add("--codec=any")               // 支持任何编解码器
    add("--no-video")                // 禁用视频输出
    add("--live-caching=300")        // 实时流缓存
    add("--clock-jitter=0")          // 时钟抖动控制
    add("--clock-synchro=0")         // 时钟同步控制
}
```

## 故障排除

### 1. 播放失败
- **检查 SDP 文件**: 确保 SDP 文件格式正确
- **检查网络**: 确保组播地址和端口可访问
- **检查推流**: 确保推流端正在发送数据

### 2. 音频质量问题
- **调整缓存**: 修改 `network-caching` 和 `live-caching` 参数
- **检查编码**: 确保推流端和播放端使用相同的音频编码格式

### 3. 延迟问题
- **降低缓存**: 减少缓存时间以降低延迟
- **使用低延迟编码**: 推荐使用 Opus 编码器

## 日志和调试

应用提供详细的日志输出：

- **VLC 初始化**: 显示 VLC 库初始化状态
- **SDP 文件**: 显示 SDP 文件内容和验证结果
- **播放状态**: 实时显示播放状态和错误信息
- **网络诊断**: 提供网络环境检查功能

## 性能优化建议

1. **使用 Opus 编码**: 提供最佳的音质和压缩比
2. **合理设置缓存**: 平衡延迟和稳定性
3. **监控内存使用**: 及时释放不需要的资源
4. **网络优化**: 确保良好的网络环境

## 扩展功能

可以进一步扩展的功能：

1. **视频支持**: 添加视频流播放支持
2. **多路流**: 支持同时播放多个音频流
3. **录制功能**: 将接收到的流录制为文件
4. **均衡器**: 添加音频均衡器功能
5. **可视化**: 添加音频频谱显示

## 注意事项

1. **权限要求**: 确保应用具有网络访问权限
2. **资源管理**: 应用退出时会自动释放 VLC 资源
3. **线程安全**: VLC 操作在后台线程中执行
4. **内存使用**: VLC 库会占用一定的内存资源

通过以上配置和使用方法，您可以充分利用 VLC Android 库的强大功能来实现高质量的音频流播放。
