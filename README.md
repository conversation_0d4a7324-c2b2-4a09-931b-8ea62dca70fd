# 组播音频推拉流测试说明

## 功能概述

新创建的 `StreamingTestActivity` 实现了通过组播地址进行音频推拉流的功能，支持：

1. **实时录音推流**：录制本地麦克风音频并推送到组播地址 `***********:15556`
2. **组播拉流播放**：从组播地址接收音频流并保存为文件
3. **FFmpeg编码**：使用AAC编码，参数与Windows命令兼容
4. **SDP文件支持**：自动生成SDP文件供拉流使用

## 文件结构

```
app/src/main/java/com/xhx/megaphone/streaming/
├── StreamingTestActivity.kt          # 主测试界面
├── MulticastStreamingHelper.kt       # 组播推流核心逻辑
└── 
app/src/main/res/layout/
└── activity_streaming_test.xml       # 测试界面布局
```

## 使用方法

### 1. 启动测试Activity

在AndroidManifest.xml中已经注册了StreamingTestActivity，可以通过以下方式启动：

```kotlin
val intent = Intent(this, StreamingTestActivity::class.java)
startActivity(intent)
```

### 2. 推流测试

1. 点击"开始推流"按钮
2. 授予录音权限（如果需要）
3. 应用开始录制麦克风音频并推送到组播地址
4. 状态显示区域会显示推流信息
5. 点击"停止推流"结束推流

### 3. 拉流测试

1. 确保有设备正在推流到组播地址
2. 点击"开始拉流"按钮
3. 应用从组播地址接收音频流并保存为WAV文件
4. 拉流完成后会显示保存路径
5. 点击"停止拉流"结束拉流

## 技术实现

### 推流实现

参考Windows FFmpeg命令：
```bash
ffmpeg -f dshow -i audio="麦克风 (Realtek(R) Audio)" -acodec aac -ab 64k -ac 1 -ar 16000 -f rtp -sdp_file stream.sdp rtp://***********:15556
```

Android实现：
```kotlin
// 1. AudioRecord录制麦克风音频
// 2. 将PCM数据写入文件/管道
// 3. FFmpeg读取数据并编码推流
val command = "-f s16le -ar 16000 -ac 1 -i ${audioFile} " +
              "-acodec aac -ab 64k -ac 1 -ar 16000 " +
              "-f rtp -sdp_file ${sdpFile} rtp://***********:15556"
```

### 拉流实现

使用生成的SDP文件进行拉流：
```kotlin
val command = "-protocol_whitelist file,udp,rtp -i ${sdpFile} " +
              "-f wav ${outputFile}"
```

### SDP文件格式

自动生成的stream.sdp内容：
```
v=0
o=- 0 0 IN IP4 127.0.0.1
s=No Name
c=IN IP4 ***********
t=0 0
a=tool:libavformat 58.45.100
m=audio 15556 RTP/AVP 97
b=AS:64
a=rtpmap:97 MPEG4-GENERIC/16000/1
a=fmtp:97 profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3; config=141056E500
```

## 测试建议

### 第一阶段：推流测试

1. 先测试推流功能是否正常工作
2. 检查日志输出，确认FFmpeg命令执行成功
3. 验证SDP文件是否正确生成
4. 可以使用网络抓包工具验证RTP数据包

### 第二阶段：拉流测试

1. 在推流正常后测试拉流功能
2. 检查生成的WAV文件是否可以播放
3. 验证音频质量和延迟

### 局域网测试

1. 在同一局域网内的多个设备上安装应用
2. 一个设备推流，其他设备拉流
3. 验证组播在局域网内的传输效果

## 故障排除

### 常见问题

1. **权限问题**：确保已授予录音权限
2. **网络问题**：检查设备是否在同一局域网
3. **FFmpeg错误**：查看日志中的FFmpeg返回码
4. **文件路径问题**：确保应用有文件读写权限

### 调试方法

1. 查看LogUtils输出的详细日志
2. 检查生成的SDP文件内容
3. 验证音频文件是否正确生成
4. 使用网络工具检查组播流量

## 拉流播放功能

### 实现原理

1. **SDP文件解析**：
   ```
   v=0
   o=- 0 0 IN IP4 127.0.0.1
   s=No Name
   c=IN IP4 ***********
   t=0 0
   a=tool:libavformat 58.45.100
   m=audio 15556 RTP/AVP 97
   b=AS:64
   a=rtpmap:97 MPEG4-GENERIC/16000/2
   a=fmtp:97 profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3; config=141056E500
   ```

2. **FFmpeg解码命令**：
   ```bash
   ffmpeg -protocol_whitelist file,udp,rtp
          -fflags +nobuffer -flags low_delay
          -probesize 32 -analyzeduration 0
          -rw_timeout 5000000
          -i stream.sdp
          -acodec pcm_s16le -ar 16000 -ac 1
          -f s16le decoded_audio.pcm
   ```

3. **实时播放**：
    - FFmpeg实时解码AAC到PCM
    - AudioTrack实时播放PCM音频
    - 支持自动重连和错误恢复

### 测试步骤

1. **启动推流**：在设备A上启动推流
2. **检查SDP**：确认SDP文件生成正确
3. **启动拉流**：在设备B上启动拉流播放
4. **验证音频**：确认能听到设备A的音频

## 扩展功能

后续可以考虑添加：

1. **音频参数配置**：支持不同采样率、码率设置
2. **多路拉流**：支持同时从多个地址拉流
3. **音频处理**：添加降噪、增益等音频处理功能
4. **录制功能**：支持将拉流音频录制保存

## 注意事项

1. 组播地址 `***********` 是本地网络组播地址
2. 端口 `15556` 需要确保没有被其他应用占用
3. 推拉流需要在同一局域网内进行测试
4. 建议在真机上测试，模拟器可能有网络限制
5. 拉流播放有轻微延迟（通常<1秒）是正常的
1. 组播地址范围限制
   局域网组播：239.0.0.0 - 239.255.255.255 （推荐使用）
   全球组播：224.0.0.0 - 238.255.255.255 （需要路由器支持）
   本地链路：224.0.0.0 - 224.0.0.255 （系统保留）
2. 端口选择建议
   避免系统端口：不要使用 1-1023
   RTP标准端口：通常使用偶数端口，如 5004, 5006, 15556
   防火墙友好：选择未被占用的端口
