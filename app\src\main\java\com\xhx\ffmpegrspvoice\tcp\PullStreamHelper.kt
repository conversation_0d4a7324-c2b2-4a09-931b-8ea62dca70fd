package com.xhx.ffmpegrspvoice.tcp

import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.blankj.utilcode.util.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.RandomAccessFile
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 拉流播放助手 - FFmpeg解码播放
 *
 * 功能：
 * 1. 通过SDP文件从组播地址拉取音频流
 * 2. FFmpeg实时解码AAC音频
 * 3. AudioTrack实时播放PCM音频
 * 4. 支持自动重连和错误恢复
 */
object PullStreamHelper {

    private const val TAG = "PullStreamHelper"

    // 音频参数 - 匹配推流参数
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
    private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT

    // 拉流状态
    private val isPulling = AtomicBoolean(false)
    private var ffmpegExecutionId: Long = 0
    private var audioTrack: AudioTrack? = null
    private var playbackThread: Thread? = null

    // 重连参数
    private var retryCount = 0
    private val maxRetries = 3
    private val retryDelayMs = 2000L

    // 文件路径
    private val cacheDir = File(App.ctx.cacheDir, "pull_streaming")
    private val outputPcmFile = File(cacheDir, "decoded_audio.pcm")

    /**
     * 开始拉流播放
     */
    fun startPulling(sdpFilePath: String): Boolean {
        if (isPulling.get()) {
            LogUtils.w(TAG, "拉流已在进行中")
            return false
        }

        // 检查SDP文件是否存在
        val sdpFile = File(sdpFilePath)
        if (!sdpFile.exists()) {
            LogUtils.e(TAG, "SDP文件不存在: $sdpFilePath")
            return false
        }

        return try {
            initializeFiles()
            startFFmpegDecoding(sdpFilePath)
            startAudioPlayback()

            isPulling.set(true)
            retryCount = 0
            LogUtils.i(TAG, "✅ 拉流播放启动成功")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ 拉流播放启动失败", e)
            stopPulling()
            false
        }
    }

    /**
     * 停止拉流
     */
    fun stopPulling() {
        if (!isPulling.get()) {
            return
        }

        isPulling.set(false)

        // 停止FFmpeg
        if (ffmpegExecutionId != 0L) {
            FFmpeg.cancel(ffmpegExecutionId)
            ffmpegExecutionId = 0
        }

        // 停止音频播放
        audioTrack?.stop()
        audioTrack?.release()
        audioTrack = null

        // 停止播放线程
        playbackThread?.interrupt()
        playbackThread = null

        LogUtils.i(TAG, "🛑 拉流已停止")
    }

    /**
     * 获取拉流状态
     */
    fun isPulling(): Boolean = isPulling.get()

    /**
     * 获取拉流信息
     */
    fun getPullInfo(): String {
        val fileSize = if (outputPcmFile.exists()) {
            "${outputPcmFile.length() / 1024}KB"
        } else {
            "0KB"
        }

        return "拉流状态: ${if (isPulling.get()) "进行中" else "已停止"}\n" +
                "重连次数: $retryCount/$maxRetries\n" +
                "解码文件: ${outputPcmFile.absolutePath}\n" +
                "文件大小: $fileSize\n" +
                "音频参数: ${SAMPLE_RATE}Hz, ${CHANNELS}声道"
    }

    /**
     * 初始化文件和目录
     */
    private fun initializeFiles() {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }

        // 清理旧文件
        if (outputPcmFile.exists()) {
            outputPcmFile.delete()
        }
    }

    /**
     * 启动FFmpeg解码 - 从SDP拉流并解码为PCM
     */
    private fun startFFmpegDecoding(sdpFilePath: String) {
        GlobalScope.launch(Dispatchers.IO) {
            // 等待推流稳定
            Thread.sleep(1000)

            // 构建FFmpeg解码命令
            // 从SDP文件读取AAC组播流，解码为PCM格式
            val command = "-protocol_whitelist file,udp,rtp " +
                    "-fflags +nobuffer " +                    // 禁用缓冲，减少延迟
                    "-flags low_delay " +                     // 低延迟模式
                    "-probesize 32 " +                        // 减少探测大小
                    "-analyzeduration 0 " +                   // 不分析流，立即开始
                    "-rw_timeout 5000000 " +                  // 5秒读写超时
                    "-i $sdpFilePath " +                      // 输入SDP文件
                    "-acodec pcm_s16le " +                    // 解码为PCM 16位小端
                    "-ar $SAMPLE_RATE " +                     // 采样率16000Hz
                    "-ac $CHANNELS " +                        // 单声道
                    "-f s16le " +                             // 输出格式为s16le
                    "${outputPcmFile.absolutePath}"           // 输出PCM文件

            LogUtils.i(TAG, "FFmpeg解码命令: $command")
            LogUtils.i(TAG, "输入SDP: $sdpFilePath")
            LogUtils.i(TAG, "输出PCM: ${outputPcmFile.absolutePath}")

            ffmpegExecutionId = FFmpeg.executeAsync(command) { executionId, returnCode ->
                LogUtils.i(TAG, "FFmpeg解码结束: executionId=$executionId, returnCode=$returnCode")

                when (returnCode) {
                    Config.RETURN_CODE_SUCCESS -> {
                        LogUtils.i(TAG, "✅ 解码正常结束")
                    }

                    Config.RETURN_CODE_CANCEL -> {
                        LogUtils.i(TAG, "🛑 解码被用户取消")
                    }

                    else -> {
                        LogUtils.w(TAG, "⚠️ 解码异常结束，返回码: $returnCode")
                        // 尝试重连
                        if (isPulling.get() && retryCount < maxRetries) {
                            retryConnection(sdpFilePath)
                        }
                    }
                }
            }
        }
    }

    /**
     * 重连机制
     */
    private fun retryConnection(sdpFilePath: String) {
        GlobalScope.launch(Dispatchers.IO) {
            retryCount++
            LogUtils.i(TAG, "🔄 尝试重连解码 ($retryCount/$maxRetries)")

            Thread.sleep(retryDelayMs)

            if (isPulling.get()) {
                startFFmpegDecoding(sdpFilePath)
            }
        }
    }

    /**
     * 启动音频播放 - 实时播放解码后的PCM音频
     */
    private fun startAudioPlayback() {
        val bufferSize = AudioTrack.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_OUT_MONO,
            AUDIO_FORMAT
        )

        audioTrack = AudioTrack(
            AudioManager.STREAM_MUSIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_OUT_MONO,
            AUDIO_FORMAT,
            bufferSize * 2, // 使用更大的缓冲区
            AudioTrack.MODE_STREAM
        )

        audioTrack?.play()
        LogUtils.i(TAG, "AudioTrack初始化完成，缓冲区大小: ${bufferSize * 2}")

        // 启动播放线程
        playbackThread = Thread {
            val buffer = ByteArray(bufferSize)
            var totalPlayed = 0
            var lastLogTime = System.currentTimeMillis()
            var lastFileSize = 0L

            LogUtils.i(TAG, "音频播放线程启动，等待PCM数据...")

            while (isPulling.get() && !Thread.currentThread().isInterrupted) {
                try {
                    if (outputPcmFile.exists()) {
                        val currentFileSize = outputPcmFile.length()

                        // 如果文件有新数据
                        if (currentFileSize > lastFileSize) {
                            RandomAccessFile(outputPcmFile, "r").use { randomAccessFile ->
                                // 从上次读取的位置开始读取
                                randomAccessFile.seek(lastFileSize)

                                var bytesRead: Int
                                while (randomAccessFile.read(buffer)
                                        .also { bytesRead = it } > 0 && isPulling.get()
                                ) {
                                    audioTrack?.write(buffer, 0, bytesRead)
                                    totalPlayed += bytesRead
                                    lastFileSize += bytesRead

                                    // 每3秒打印一次状态
                                    val currentTime = System.currentTimeMillis()
                                    if (currentTime - lastLogTime > 3000) {
                                        LogUtils.d(
                                            TAG,
                                            "🔊 播放PCM音频: ${totalPlayed / 1024}KB, 文件大小: ${currentFileSize / 1024}KB"
                                        )
                                        lastLogTime = currentTime
                                    }
                                }
                            }
                        } else {
                            // 没有新数据，等待
                            Thread.sleep(50)
                        }
                    } else {
                        // 文件不存在，等待FFmpeg开始解码
                        Thread.sleep(200)
                    }
                } catch (e: Exception) {
                    LogUtils.e(TAG, "音频播放异常", e)
                    Thread.sleep(500) // 出错后等待500ms再重试
                }
            }

            LogUtils.i(TAG, "音频播放线程结束，总计播放: ${totalPlayed / 1024}KB")
        }

        playbackThread?.start()
    }
}
