package com.xhx.ffmpegrspvoice.tcp

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.blankj.utilcode.util.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 实时推流助手 - 最优化版本
 * 
 * 功能：
 * 1. 录音buffer直接实时写入临时文件
 * 2. FFmpeg同时读取文件进行推流
 * 3. 最小化延迟的实时推流
 */
object LiveStreamingHelper {
    
    private const val TAG = "LiveStreamingHelper"
    
    // 组播配置
    private const val MULTICAST_ADDRESS = "*********"
    private const val MULTICAST_PORT = 15556
    
    // 音频参数
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
    private const val BIT_RATE = 64000
    private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
    
    // 缓冲区大小 - 使用较小的缓冲区减少延迟
    private val BUFFER_SIZE = AudioRecord.getMinBufferSize(
        SAMPLE_RATE,
        AudioFormat.CHANNEL_IN_MONO,
        AUDIO_FORMAT
    ).let { minSize ->
        // 使用最小缓冲区的2倍，减少延迟
        minSize * 2
    }
    
    // 推流状态
    private val isStreaming = AtomicBoolean(false)
    private var audioRecord: AudioRecord? = null
    private var recordingThread: Thread? = null
    private var ffmpegExecutionId: Long = 0
    
    // 文件路径
    private val cacheDir = File(App.ctx.cacheDir, "live_streaming")
    private val sdpFile = File(cacheDir, "stream.sdp")
    private val liveAudioFile = File(cacheDir, "live_audio.pcm")
    
    /**
     * 开始实时录音推流
     */
    fun startStreaming(): Boolean {
        if (isStreaming.get()) {
            LogUtils.w(TAG, "推流已在进行中")
            return false
        }
        
        return try {
            initializeFiles()
            createSdpFile()
            startAudioRecording()
            startLiveStreaming()
            
            isStreaming.set(true)
            LogUtils.i(TAG, "✅ 实时推流启动成功")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ 实时推流启动失败", e)
            stopStreaming()
            false
        }
    }
    
    /**
     * 停止推流
     */
    fun stopStreaming() {
        if (!isStreaming.get()) {
            return
        }
        
        isStreaming.set(false)
        
        // 停止录音
        audioRecord?.stop()
        audioRecord?.release()
        audioRecord = null
        
        // 停止录音线程
        recordingThread?.interrupt()
        recordingThread = null
        
        // 停止FFmpeg
        if (ffmpegExecutionId != 0L) {
            FFmpeg.cancel(ffmpegExecutionId)
            ffmpegExecutionId = 0
        }
        
        LogUtils.i(TAG, "🛑 实时推流已停止")
    }
    
    /**
     * 获取推流状态
     */
    fun isStreaming(): Boolean = isStreaming.get()
    
    /**
     * 获取SDP文件路径
     */
    fun getSdpFilePath(): String = sdpFile.absolutePath
    
    /**
     * 获取组播地址信息
     */
    fun getMulticastInfo(): String {
        val fileSize = if (liveAudioFile.exists()) {
            "${liveAudioFile.length() / 1024}KB"
        } else {
            "0KB"
        }
        
        return "组播地址: $MULTICAST_ADDRESS:$MULTICAST_PORT\n" +
                "SDP文件: ${sdpFile.absolutePath}\n" +
                "传输方式: 实时文件流\n" +
                "缓冲区大小: ${BUFFER_SIZE}字节\n" +
                "当前文件大小: $fileSize\n" +
                "推流状态: ${if (isStreaming.get()) "进行中" else "已停止"}"
    }
    
    /**
     * 初始化文件和目录
     */
    private fun initializeFiles() {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        
        // 清理旧文件
        if (liveAudioFile.exists()) {
            liveAudioFile.delete()
        }
        
        // 创建新的音频文件
        liveAudioFile.createNewFile()
    }
    
    /**
     * 创建SDP文件
     */
    private fun createSdpFile() {
        val sdpContent = """
            v=0
            o=- 0 0 IN IP4 127.0.0.1
            s=No Name
            c=IN IP4 $MULTICAST_ADDRESS
            t=0 0
            a=tool:libavformat 58.45.100
            m=audio $MULTICAST_PORT RTP/AVP 97
            b=AS:64
            a=rtpmap:97 MPEG4-GENERIC/$SAMPLE_RATE/$CHANNELS
            a=fmtp:97 profile-level-id=1;mode=AAC-hbr;sizelength=13;indexlength=3;indexdeltalength=3; config=141056E500
        """.trimIndent()
        
        sdpFile.writeText(sdpContent)
        LogUtils.i(TAG, "SDP文件创建成功: ${sdpFile.absolutePath}")
    }
    
    /**
     * 开始音频录音 - 直接实时写入文件
     */
    private fun startAudioRecording() {
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AUDIO_FORMAT,
            BUFFER_SIZE
        )
        
        if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
            throw IOException("AudioRecord初始化失败")
        }
        
        audioRecord?.startRecording()
        
        // 启动录音线程，实时写入文件
        recordingThread = Thread {
            val buffer = ByteArray(BUFFER_SIZE)
            var fileOutputStream: FileOutputStream? = null
            var totalBytes = 0
            var lastLogTime = System.currentTimeMillis()
            
            try {
                fileOutputStream = FileOutputStream(liveAudioFile, false) // 不追加，覆盖写入
                LogUtils.i(TAG, "录音线程启动，实时写入: ${liveAudioFile.absolutePath}")
                LogUtils.i(TAG, "缓冲区大小: $BUFFER_SIZE 字节")
                
                while (isStreaming.get() && !Thread.currentThread().isInterrupted) {
                    val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                    if (bytesRead > 0) {
                        // 立即写入文件并刷新
                        fileOutputStream.write(buffer, 0, bytesRead)
                        fileOutputStream.flush()
                        totalBytes += bytesRead
                        
                        // 每3秒打印一次状态（更频繁的状态更新）
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastLogTime > 3000) {
                            LogUtils.d(TAG, "🎙️ 实时录音中: ${totalBytes / 1024}KB, 速率: ${(totalBytes / ((currentTime - (lastLogTime - 3000)) / 1000.0) / 1024).toInt()}KB/s")
                            lastLogTime = currentTime
                        }
                    } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                        LogUtils.e(TAG, "AudioRecord读取错误: ERROR_INVALID_OPERATION")
                        break
                    } else if (bytesRead < 0) {
                        LogUtils.w(TAG, "AudioRecord读取返回负值: $bytesRead")
                    }
                }
                
                LogUtils.i(TAG, "录音线程结束，总计: ${totalBytes / 1024}KB")
            } catch (e: Exception) {
                LogUtils.e(TAG, "录音数据写入异常", e)
            } finally {
                fileOutputStream?.close()
            }
        }
        
        recordingThread?.start()
        LogUtils.i(TAG, "音频录音已启动")
    }
    
    /**
     * 启动实时推流
     */
    private fun startLiveStreaming() {
        GlobalScope.launch(Dispatchers.IO) {
            // 等待一些音频数据写入
            Thread.sleep(300)
            
            // 构建FFmpeg命令 - 使用较小的缓冲区和实时参数
            val command = "-re -f s16le -ar $SAMPLE_RATE -ac $CHANNELS " +
                    "-thread_queue_size 512 " +  // 增加线程队列大小
                    "-i ${liveAudioFile.absolutePath} " +
                    "-acodec aac -ab ${BIT_RATE/1000}k -ac $CHANNELS -ar $SAMPLE_RATE " +
                    "-f rtp -sdp_file ${sdpFile.absolutePath} " +
                    "rtp://$MULTICAST_ADDRESS:$MULTICAST_PORT"
            
            LogUtils.i(TAG, "FFmpeg实时推流命令: $command")
            
            ffmpegExecutionId = FFmpeg.executeAsync(command) { executionId, returnCode ->
                LogUtils.i(TAG, "FFmpeg推流结束: executionId=$executionId, returnCode=$returnCode")
                
                when (returnCode) {
                    Config.RETURN_CODE_SUCCESS -> {
                        LogUtils.i(TAG, "✅ 推流正常结束")
                    }
                    Config.RETURN_CODE_CANCEL -> {
                        LogUtils.i(TAG, "🛑 推流被用户取消")
                    }
                    else -> {
                        LogUtils.w(TAG, "⚠️ 推流异常结束，返回码: $returnCode")
                    }
                }
            }
            
            LogUtils.i(TAG, "FFmpeg执行ID: $ffmpegExecutionId")
        }
    }
}
