pluginManagement {
    repositories {
        maven { setUrl("https://maven.aliyun.com/repository/gradle-plugin")  }
        maven { setUrl( "https://maven.aliyun.com/repository/public") }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { setUrl("https://maven.aliyun.com/repository/public")  }
        maven { setUrl("https://maven.aliyun.com/repository/google")   }
        maven { setUrl("https://maven.aliyun.com/repository/central")   }
        google()
        mavenCentral()
    }
}

rootProject.name = "FFmpegRspVoice"
include(":app")
 