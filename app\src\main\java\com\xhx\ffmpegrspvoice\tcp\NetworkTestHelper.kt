package com.xhx.ffmpegrspvoice.tcp

import android.util.Log
import com.blankj.utilcode.util.LogUtils
import java.net.*
import java.io.IOException

/**
 * 网络测试助手
 * 用于测试组播和网络连接
 */
object NetworkTestHelper {
    
    private const val TAG = "NetworkTestHelper"
    
    /**
     * 测试组播连接
     */
    fun testMulticastConnection(
        multicastAddress: String = "*********",
        port: Int = 15556
    ): <PERSON><PERSON>an {
        return try {
            LogUtils.i(TAG, "测试组播连接: $multicastAddress:$port")
            
            // 创建组播套接字
            val socket = MulticastSocket(port)
            val group = InetAddress.getByName(multicastAddress)
            
            // 加入组播组
            socket.joinGroup(group)
            LogUtils.i(TAG, "✅ 成功加入组播组: $multicastAddress")
            
            // 设置超时
            socket.soTimeout = 5000
            
            // 尝试接收数据包
            val buffer = ByteArray(1024)
            val packet = DatagramPacket(buffer, buffer.size)
            
            LogUtils.i(TAG, "等待接收组播数据包...")
            try {
                socket.receive(packet)
                LogUtils.i(TAG, "✅ 接收到组播数据包: ${packet.length} bytes from ${packet.address}")
                
                // 离开组播组
                socket.leaveGroup(group)
                socket.close()
                
                return true
            } catch (e: SocketTimeoutException) {
                LogUtils.w(TAG, "⚠️ 组播接收超时 - 可能没有数据流")
                
                // 离开组播组
                socket.leaveGroup(group)
                socket.close()
                
                return false
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ 组播连接测试失败", e)
            false
        }
    }
    
    /**
     * 测试网络接口
     */
    fun testNetworkInterfaces(): String {
        val result = StringBuilder()
        
        try {
            result.append("📡 网络接口信息:\n")
            
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                if (networkInterface.isUp && !networkInterface.isLoopback) {
                    result.append("接口: ${networkInterface.name}\n")
                    result.append("显示名: ${networkInterface.displayName}\n")
                    result.append("支持组播: ${networkInterface.supportsMulticast()}\n")
                    
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (address is Inet4Address) {
                            result.append("IPv4地址: ${address.hostAddress}\n")
                        }
                    }
                    result.append("\n")
                }
            }
            
        } catch (e: Exception) {
            result.append("❌ 获取网络接口信息失败: ${e.message}\n")
        }
        
        return result.toString()
    }
    
    /**
     * 测试UDP端口连接
     */
    fun testUdpPort(port: Int = 15556): Boolean {
        return try {
            LogUtils.i(TAG, "测试UDP端口: $port")
            
            val socket = DatagramSocket(port)
            socket.soTimeout = 1000
            
            LogUtils.i(TAG, "✅ UDP端口 $port 可用")
            socket.close()
            true
            
        } catch (e: BindException) {
            LogUtils.w(TAG, "⚠️ UDP端口 $port 已被占用")
            false
        } catch (e: Exception) {
            LogUtils.e(TAG, "❌ UDP端口测试失败", e)
            false
        }
    }
    
    /**
     * 获取本机IP地址
     */
    fun getLocalIpAddress(): String? {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                if (networkInterface.isUp && !networkInterface.isLoopback) {
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (address is Inet4Address && !address.isLoopbackAddress) {
                            return address.hostAddress
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取本机IP地址失败", e)
        }
        return null
    }
    
    /**
     * 执行完整的网络诊断
     */
    fun performNetworkDiagnosis(): String {
        val result = StringBuilder()
        
        result.append("🔍 网络诊断报告\n")
        result.append("=" * 40 + "\n\n")
        
        // 本机IP
        val localIp = getLocalIpAddress()
        result.append("本机IP地址: ${localIp ?: "未知"}\n\n")
        
        // 网络接口
        result.append(testNetworkInterfaces())
        
        // UDP端口测试
        val udpPortAvailable = testUdpPort(15556)
        result.append("UDP端口15556状态: ${if (udpPortAvailable) "可用" else "不可用"}\n\n")
        
        // 组播测试
        result.append("组播连接测试:\n")
        val multicastWorking = testMulticastConnection()
        result.append("组播接收状态: ${if (multicastWorking) "正常" else "异常"}\n\n")
        
        return result.toString()
    }
}
